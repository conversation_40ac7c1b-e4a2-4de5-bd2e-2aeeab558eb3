global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'miss-taskmaster-mcp'
    static_configs:
      - targets: ['mcp-server:8000']
    scrape_interval: 10s
    metrics_path: '/metrics'

  - job_name: 'miss-taskmaster-health'
    static_configs:
      - targets: ['mcp-server:8000']
    scrape_interval: 30s
    metrics_path: '/health/metrics'

  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']