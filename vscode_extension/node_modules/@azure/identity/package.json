{"name": "@azure/identity", "sdk-type": "client", "version": "4.12.0", "description": "Provides credential implementations for Azure SDK libraries that can authenticate with Microsoft Entra ID", "main": "./dist/commonjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/commonjs/index.d.ts", "browser": "./dist/browser/index.js", "//sampleConfiguration": {"productName": "Azure Identity", "productSlugs": ["entra", "entra-id"], "skip": ["interactiveBrowserCredential.js"], "requiredResources": {"Register an app with the Microsoft identity platform": "https://learn.microsoft.com/entra/identity-platform/quickstart-register-app", "Set and retrieve a secret from Azure Key Vault": "https://learn.microsoft.com/azure/key-vault/secrets/quick-create-portal"}}, "files": ["dist/", "README.md", "LICENSE"], "//metadata": {"constantPaths": [{"path": "src/constants.ts", "prefix": "SDK_VERSION"}]}, "engines": {"node": ">=20.0.0"}, "repository": "github:Azure/azure-sdk-for-js", "keywords": ["azure", "cloud", "entra id", "authentication", "credential", "certificate", "managed identity", "client secret", "access token"], "author": "Microsoft Corporation", "license": "MIT", "bugs": {"url": "https://github.com/Azure/azure-sdk-for-js/issues"}, "homepage": "https://github.com/Azure/azure-sdk-for-js/tree/main/sdk/identity/identity/README.md", "sideEffects": false, "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-auth": "^1.9.0", "@azure/core-client": "^1.9.2", "@azure/core-rest-pipeline": "^1.17.0", "@azure/core-tracing": "^1.0.0", "@azure/core-util": "^1.11.0", "@azure/logger": "^1.0.0", "@azure/msal-browser": "^4.2.0", "@azure/msal-node": "^3.5.0", "open": "^10.1.0", "tslib": "^2.2.0"}, "devDependencies": {"@azure/keyvault-keys": "4.10.0", "@types/jsonwebtoken": "^9.0.0", "@types/ms": "^2.1.0", "@types/node": "^20.19.0", "@vitest/browser": "^3.2.3", "@vitest/coverage-istanbul": "^3.2.3", "assertion-error": "^2.0.1", "dotenv": "^16.0.0", "eslint": "^9.33.0", "jsonwebtoken": "^9.0.0", "ms": "^2.1.3", "playwright": "^1.50.1", "typescript": "~5.8.3", "vitest": "^3.2.3", "@azure-tools/test-recorder": "^4.1.1", "@azure-tools/test-utils-vitest": "^2.0.1", "@azure/eslint-plugin-azure-sdk": "^3.0.0", "@azure/dev-tool": "^1.0.0"}, "type": "module", "tshy": {"project": "../../../tsconfig.src.build.json", "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}, "dialects": ["esm", "commonjs"], "esmDialects": ["workerd", "browser"], "selfLink": false}, "exports": {"./package.json": "./package.json", ".": {"workerd": {"types": "./dist/workerd/index.d.ts", "default": "./dist/workerd/index.js"}, "browser": {"types": "./dist/browser/index.d.ts", "default": "./dist/browser/index.js"}, "import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "scripts": {"build": "npm run clean && dev-tool run build-package && dev-tool run extract-api", "build:samples": "tsc -p tsconfig.samples.json", "check-format": "dev-tool run vendored prettier --list-different --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.ts\" \"test/**/*.ts\" \"samples-dev/**/*.ts\" \"*.{js,json}\"", "clean": "dev-tool run vendored rimraf --glob dist dist-* types *.tgz *.log", "execute:samples": "dev-tool samples run samples-dev", "extract-api": "dev-tool run build-package && dev-tool run extract-api", "format": "dev-tool run vendored prettier --write --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.ts\" \"test/**/*.ts\" \"samples-dev/**/*.ts\" \"*.{js,json}\"", "lint": "eslint package.json src test --ignore-pattern 'test/integration/**'", "lint:fix": "eslint package.json src test --fix --fix-type [problem,suggestion] --ignore-pattern 'test/integration/**'", "pack": "pnpm pack 2>&1", "test": "npm run test:node && npm run test:browser", "test:browser": "npm run clean && dev-tool run build-package && dev-tool run build-test && dev-tool run test:vitest --browser", "test:node": "dev-tool run build-test --no-browser-test && dev-tool run test:vitest", "test:node:managed-identity": "dev-tool run test:vitest -- --config ./vitest.managed-identity.config.ts", "test:node:no-timeouts": "dev-tool run test:vitest -- --test-timeout=0", "update-snippets": "dev-tool run update-snippets"}}