import { ResetPasswordSubmitPasswordResult } from "../result/ResetPasswordSubmitPasswordResult.js";
import { ResetPasswordState } from "./ResetPasswordState.js";
import { ResetPasswordPasswordRequiredStateParameters } from "./ResetPasswordStateParameters.js";
export declare class ResetPasswordPasswordRequiredState extends ResetPasswordState<ResetPasswordPasswordRequiredStateParameters> {
    /**
     * Submits a new password for reset password flow.
     * @param {string} password - The password to submit.
     * @returns {Promise<ResetPasswordSubmitPasswordResult>} The result of the operation.
     */
    submitNewPassword(password: string): Promise<ResetPasswordSubmitPasswordResult>;
}
//# sourceMappingURL=ResetPasswordPasswordRequiredState.d.ts.map