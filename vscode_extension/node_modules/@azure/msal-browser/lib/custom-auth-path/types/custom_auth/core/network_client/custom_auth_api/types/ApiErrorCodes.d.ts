export declare const CONTINUATION_TOKEN_MISSING = "continuation_token_missing";
export declare const INVALID_RESPONSE_BODY = "invalid_response_body";
export declare const EMPTY_RESPONSE = "empty_response";
export declare const UNSUPPORTED_CHALLENGE_TYPE = "unsupported_challenge_type";
export declare const ACCESS_TOKEN_MISSING = "access_token_missing";
export declare const ID_TOKEN_MISSING = "id_token_missing";
export declare const REFRESH_TOKEN_MISSING = "refresh_token_missing";
export declare const INVALID_EXPIRES_IN = "invalid_expires_in";
export declare const INVALID_TOKEN_TYPE = "invalid_token_type";
export declare const HTTP_REQUEST_FAILED = "http_request_failed";
export declare const INVALID_REQUEST = "invalid_request";
export declare const USER_NOT_FOUND = "user_not_found";
export declare const INVALID_GRANT = "invalid_grant";
export declare const CREDENTIAL_REQUIRED = "credential_required";
export declare const ATTRIBUTES_REQUIRED = "attributes_required";
export declare const USER_ALREADY_EXISTS = "user_already_exists";
export declare const INVALID_POLL_STATUS = "invalid_poll_status";
export declare const PASSWORD_CHANGE_FAILED = "password_change_failed";
export declare const PASSWORD_RESET_TIMEOUT = "password_reset_timeout";
export declare const CLIENT_INFO_MISSING = "client_info_missing";
export declare const EXPIRED_TOKEN = "expired_token";
//# sourceMappingURL=ApiErrorCodes.d.ts.map