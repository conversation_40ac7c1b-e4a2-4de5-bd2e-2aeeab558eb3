{"version": 3, "file": "GetAccessTokenResult.d.ts", "sourceRoot": "", "sources": ["../../../../../../../src/custom_auth/get_account/auth_flow/result/GetAccessTokenResult.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,oBAAoB,EAAE,MAAM,8CAA8C,CAAC;AACpF,OAAO,EAAE,kBAAkB,EAAE,MAAM,+CAA+C,CAAC;AACnF,OAAO,EAAE,iCAAiC,EAAE,MAAM,kCAAkC,CAAC;AACrF,OAAO,EACH,4BAA4B,EAC5B,yBAAyB,EAC5B,MAAM,iCAAiC,CAAC;AAKzC,qBAAa,oBAAqB,SAAQ,kBAAkB,CACxD,yBAAyB,EACzB,iCAAiC,EACjC,oBAAoB,CACvB;IACG;;;OAGG;gBACS,UAAU,CAAC,EAAE,oBAAoB;IAI7C;;;;OAIG;IACH,MAAM,CAAC,eAAe,CAAC,KAAK,EAAE,OAAO,GAAG,oBAAoB;IAU5D;;OAEG;IACH,WAAW,IAAI,IAAI,IAAI,oBAAoB,GAAG;QAC1C,KAAK,EAAE,4BAA4B,CAAC;KACvC;IAID;;OAEG;IACH,QAAQ,IAAI,IAAI,IAAI,oBAAoB,GAAG;QACvC,KAAK,EAAE,yBAAyB,CAAC;KACpC;CAGJ;AAED;;;;;GAKG;AACH,MAAM,MAAM,yBAAyB,GAC/B,4BAA4B,GAC5B,yBAAyB,CAAC"}