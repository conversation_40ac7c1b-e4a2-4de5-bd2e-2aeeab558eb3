export declare function ensureArgumentIsNotNullOrUndefined<T>(argName: string, argValue: T | undefined | null, correlationId?: string): asserts argValue is T;
export declare function ensureArgumentIsNotEmptyString(argName: string, argValue: string | undefined, correlationId?: string): void;
export declare function ensureArgumentIsJSONString(argName: string, argValue: string, correlationId?: string): void;
//# sourceMappingURL=ArgumentValidator.d.ts.map