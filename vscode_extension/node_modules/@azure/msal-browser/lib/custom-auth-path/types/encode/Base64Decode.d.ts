/**
 * Class which exposes APIs to decode base64 strings to plaintext. See here for implementation details:
 * https://developer.mozilla.org/en-US/docs/Glossary/Base64#the_unicode_problem
 */
/**
 * Returns a URL-safe plaintext decoded string from b64 encoded input.
 * @param input
 */
export declare function base64Decode(input: string): string;
/**
 * Decodes base64 into Uint8Array
 * @param base64String
 */
export declare function base64DecToArr(base64String: string): Uint8Array;
//# sourceMappingURL=Base64Decode.d.ts.map