export declare const pkceNotCreated = "pkce_not_created";
export declare const earJwkEmpty = "ear_jwk_empty";
export declare const earJweEmpty = "ear_jwe_empty";
export declare const cryptoNonExistent = "crypto_nonexistent";
export declare const emptyNavigateUri = "empty_navigate_uri";
export declare const hashEmptyError = "hash_empty_error";
export declare const noStateInHash = "no_state_in_hash";
export declare const hashDoesNotContainKnownProperties = "hash_does_not_contain_known_properties";
export declare const unableToParseState = "unable_to_parse_state";
export declare const stateInteractionTypeMismatch = "state_interaction_type_mismatch";
export declare const interactionInProgress = "interaction_in_progress";
export declare const popupWindowError = "popup_window_error";
export declare const emptyWindowError = "empty_window_error";
export declare const userCancelled = "user_cancelled";
export declare const monitorPopupTimeout = "monitor_popup_timeout";
export declare const monitorWindowTimeout = "monitor_window_timeout";
export declare const redirectInIframe = "redirect_in_iframe";
export declare const blockIframeReload = "block_iframe_reload";
export declare const blockNestedPopups = "block_nested_popups";
export declare const iframeClosedPrematurely = "iframe_closed_prematurely";
export declare const silentLogoutUnsupported = "silent_logout_unsupported";
export declare const noAccountError = "no_account_error";
export declare const silentPromptValueError = "silent_prompt_value_error";
export declare const noTokenRequestCacheError = "no_token_request_cache_error";
export declare const unableToParseTokenRequestCacheError = "unable_to_parse_token_request_cache_error";
export declare const authRequestNotSetError = "auth_request_not_set_error";
export declare const invalidCacheType = "invalid_cache_type";
export declare const nonBrowserEnvironment = "non_browser_environment";
export declare const databaseNotOpen = "database_not_open";
export declare const noNetworkConnectivity = "no_network_connectivity";
export declare const postRequestFailed = "post_request_failed";
export declare const getRequestFailed = "get_request_failed";
export declare const failedToParseResponse = "failed_to_parse_response";
export declare const unableToLoadToken = "unable_to_load_token";
export declare const cryptoKeyNotFound = "crypto_key_not_found";
export declare const authCodeRequired = "auth_code_required";
export declare const authCodeOrNativeAccountIdRequired = "auth_code_or_nativeAccountId_required";
export declare const spaCodeAndNativeAccountIdPresent = "spa_code_and_nativeAccountId_present";
export declare const databaseUnavailable = "database_unavailable";
export declare const unableToAcquireTokenFromNativePlatform = "unable_to_acquire_token_from_native_platform";
export declare const nativeHandshakeTimeout = "native_handshake_timeout";
export declare const nativeExtensionNotInstalled = "native_extension_not_installed";
export declare const nativeConnectionNotEstablished = "native_connection_not_established";
export declare const uninitializedPublicClientApplication = "uninitialized_public_client_application";
export declare const nativePromptNotSupported = "native_prompt_not_supported";
export declare const invalidBase64String = "invalid_base64_string";
export declare const invalidPopTokenRequest = "invalid_pop_token_request";
export declare const failedToBuildHeaders = "failed_to_build_headers";
export declare const failedToParseHeaders = "failed_to_parse_headers";
export declare const failedToDecryptEarResponse = "failed_to_decrypt_ear_response";
export declare const timedOut = "timed_out";
//# sourceMappingURL=BrowserAuthErrorCodes.d.ts.map