{"version": 3, "file": "PlatformAuthDOMHandler.d.ts", "sourceRoot": "", "sources": ["../../../../../src/broker/nativeBroker/PlatformAuthDOMHandler.ts"], "names": [], "mappings": "AAKA,OAAO,EACH,MAAM,EAGN,kBAAkB,EAErB,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAEH,mBAAmB,EAEtB,MAAM,0BAA0B,CAAC;AAElC,OAAO,EACH,oBAAoB,EAEvB,MAAM,2BAA2B,CAAC;AAEnC,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AAEjE,qBAAa,sBAAuB,YAAW,oBAAoB;IAC/D,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;IACzB,SAAS,CAAC,iBAAiB,EAAE,kBAAkB,CAAC;IAChD,SAAS,CAAC,aAAa,EAAE,MAAM,CAAC;IAChC,gBAAgB,EAAE,MAAM,CAAC;gBAGrB,MAAM,EAAE,MAAM,EACd,iBAAiB,EAAE,kBAAkB,EACrC,aAAa,EAAE,MAAM;WAQZ,cAAc,CACvB,MAAM,EAAE,MAAM,EACd,iBAAiB,EAAE,kBAAkB,EACrC,aAAa,EAAE,MAAM,GACtB,OAAO,CAAC,sBAAsB,GAAG,SAAS,CAAC;IA0B9C;;;OAGG;IACH,cAAc,IAAI,MAAM;IAIxB,mBAAmB,IAAI,MAAM,GAAG,SAAS;IAIzC,gBAAgB,IAAI,MAAM,GAAG,SAAS;IAItC;;;;OAIG;IACG,WAAW,CACb,OAAO,EAAE,mBAAmB,GAC7B,OAAO,CAAC,oBAAoB,CAAC;IAsBhC,OAAO,CAAC,4BAA4B;IA0CpC,OAAO,CAAC,8BAA8B;IAiDtC,OAAO,CAAC,+BAA+B;IAsBvC,OAAO,CAAC,iBAAiB;CAiB5B"}